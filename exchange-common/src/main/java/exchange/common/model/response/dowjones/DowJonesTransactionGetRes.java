package exchange.common.model.response.dowjones;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: wen.y
 * @date: 2024/12/14
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DowJonesTransactionGetRes {

	@JsonProperty("data")
	private Data data;

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Data {
		@JsonProperty("id")
		private String id;

		// transactions
		@JsonProperty("type")
		private String type;

		@JsonProperty("attributes")
		private Attributes attributes;

	}

	@JsonIgnoreProperties(ignoreUnknown = true)
	@lombok.Data
	public static class Attributes {
		@JsonProperty("operation")
		private String operation;

		/**
		 * COMPLETED
		 */
		@JsonProperty("status")
		private String status;

		@JsonProperty("case_id")
		private String caseId;

		@JsonProperty("valid_associations")
		private Integer validAssociations;

		@JsonProperty("invalid_associations")
		private Integer invalidAssociations;

		@JsonProperty("pending_associations")
		private Integer pendingAssociations;

		@JsonProperty("processing_associations")
		private Integer processingAssociations;
	}

}
