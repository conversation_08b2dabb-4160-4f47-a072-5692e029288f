package exchange.common.entity;

import java.math.BigDecimal;
import javax.annotation.Generated;
import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import exchange.common.constant.Exchange;
import exchange.common.constant.OrderSide;
import exchange.common.constant.OrderType;
import exchange.common.constant.TradeAction;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(CopyTrade.class)
public abstract class CopyTrade_ extends AbstractEntity_ {

  public static volatile SingularAttribute<CopyTrade, Long> symbolId;
  public static volatile SingularAttribute<CopyTrade, Long> userId;
  public static volatile SingularAttribute<CopyTrade, OrderSide> orderSide;
  public static volatile SingularAttribute<CopyTrade, OrderType> orderType;
  public static volatile SingularAttribute<CopyTrade, BigDecimal> price;
  public static volatile SingularAttribute<CopyTrade, BigDecimal> amount;
  public static volatile SingularAttribute<CopyTrade, TradeAction> tradeAction;
  public static volatile SingularAttribute<CopyTrade, Long> orderId;
  public static volatile SingularAttribute<CopyTrade, BigDecimal> fee;
  public static volatile SingularAttribute<CopyTrade, BigDecimal> jpyConversion;
  public static volatile SingularAttribute<CopyTrade, Long> targetOrderId;
  public static volatile SingularAttribute<CopyTrade, Long> targetUserId;
  public static volatile SingularAttribute<CopyTrade, BigDecimal> assetAmount;
  public static volatile SingularAttribute<CopyTrade, Exchange> exchange;
  public static volatile SingularAttribute<CopyTrade, Long> exchangeId;
  public static volatile SingularAttribute<CopyTrade, Long> coverOrderId;

  public static final String SYMBOL_ID = "symbolId";
  public static final String USER_ID = "userId";
  public static final String ORDER_SIDE = "orderSide";
  public static final String ORDER_TYPE = "orderType";
  public static final String PRICE = "price";
  public static final String AMOUNT = "amount";
  public static final String TRADE_ACTION = "tradeAction";
  public static final String ORDER_ID = "orderId";
  public static final String FEE = "fee";
  public static final String JPY_CONVERSION = "jpyConversion";
  public static final String TARGET_ORDER_ID = "targetOrderId";
  public static final String TARGET_USER_ID = "targetUserId";
  public static final String ASSET_AMOUNT = "assetAmount";
  public static final String EXCHANGE = "exchange";
  public static final String EXCHANGE_ID = "exchangeId";
  public static final String COVER_ORDER_ID = "coverOrderId";
}
