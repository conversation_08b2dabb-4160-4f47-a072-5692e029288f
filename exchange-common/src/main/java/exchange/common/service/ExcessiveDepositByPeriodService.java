package exchange.common.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.criteria.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.DataSourceManager;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.Currency;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.TmsStatus;
import exchange.common.constant.TradeType;
import exchange.common.entity.Candlestick;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Deposit;
import exchange.common.entity.ExcessiveDepositByPeriod;
import exchange.common.entity.ExcessiveDepositByPeriod_;
import exchange.common.entity.User;
import exchange.common.predicate.ExcessiveDepositByPeriodPredicate;
import exchange.common.util.DateUnit;
import exchange.pos.service.PosCandlestickService;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ExcessiveDepositByPeriodService
    extends EntityService<ExcessiveDepositByPeriod, ExcessiveDepositByPeriodPredicate> {

  private final DepositService depositService;
  private final SymbolService symbolService;
  private final DataSourceManager dataSourceManager;
  private final PosCandlestickService posCandlestickService;
  private final CurrencyPairConfigService currencyPairConfigService;

  public record ReadData(List<DepositsByCurrency> depositsByCurrencies, LocalDate date) {
  }
  public record DepositsByCurrency(Currency currency, BigDecimal jpyPrice, List<Deposit> deposits) {
  }

  public record WriteData(List<ExcessiveDepositByPeriod> ExcessiveDeposits, LocalDate date) {
  }

  public Page<ExcessiveDepositByPeriod> findAllByCondition(Long userId, 
      Long dateFrom, Long dateTo, TmsStatus status, Pageable pageable) throws Exception {
    var entityManager = dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    var criteriaBuilder = entityManager.getCriteriaBuilder();
    var criteriaQuery = criteriaBuilder.createQuery(ExcessiveDepositByPeriod.class);
    var root = criteriaQuery.from(ExcessiveDepositByPeriod.class);

    var predicates = new ArrayList<Predicate>();
    // add condition
    if (userId != null) {
      predicates
          .add(criteriaBuilder.equal(root.get(ExcessiveDepositByPeriod_.userId), userId));
    }
    if (dateFrom != null) {
      predicates.add(criteriaBuilder.greaterThanOrEqualTo(
          root.get(ExcessiveDepositByPeriod_.targetAt), new Date(dateFrom)));
    }
    if (dateTo != null) {
      predicates.add(criteriaBuilder.lessThan(root.get(ExcessiveDepositByPeriod_.targetAt),
          new Date(dateTo + DateUnit.DAY.getMillis())));
    }
    if (status != null) {
      predicates
          .add(criteriaBuilder.equal(root.get(ExcessiveDepositByPeriod_.tmsStatus), status));
    }

    criteriaQuery.select(root).where(predicates.toArray(new Predicate[] {}));

    var total = entityManager.createQuery(criteriaQuery).getResultList().size();
    criteriaQuery.orderBy(criteriaBuilder.desc(root.get(ExcessiveDepositByPeriod_.ID)));
    
    var typedQuery = entityManager.createQuery(criteriaQuery);
    if (pageable.isPaged()) {
      typedQuery = typedQuery
        .setFirstResult(pageable.getPageNumber() * pageable.getPageSize())
        .setMaxResults(pageable.getPageSize());
    } 
    var excessiveDeposits = typedQuery.getResultList();

    entityManager.close();
    return new PageImpl<>(excessiveDeposits, pageable, total);
  }
  

  public void deleteByTargetAt(LocalDate date) {
    var entityManager = dataSourceManager.getMasterEntityManagerFactory().createEntityManager();
    var criteriaBuilder = entityManager.getCriteriaBuilder();
    var criteriaDelete = criteriaBuilder.createCriteriaDelete(ExcessiveDepositByPeriod.class);
    var root = criteriaDelete.from(ExcessiveDepositByPeriod.class);

    var predicate = criteriaBuilder.equal(root.get(ExcessiveDepositByPeriod_.targetAt),
        Date.from(date.atStartOfDay(ZoneId.of("Asia/Tokyo")).toInstant()));

    criteriaDelete.where(predicate);
    entityManager.getTransaction().begin();
    entityManager.createQuery(criteriaDelete).executeUpdate();
    entityManager.getTransaction().commit();
    entityManager.close();
  }

  public void apply(Long id, TmsStatus tmsStatus) throws Exception {
    ExcessiveDepositByPeriod entity = findOne(id);
    if (entity == null) {
      return;
    }
    entity.setTmsStatus(tmsStatus);
    save(entity);
  }


  // JPY換算価格を取得
  public BigDecimal getJpyPrice(Currency currency, LocalDate date) {
//    Candlestick latest = null;
//    if (currency == Currency.ADA || currency == Currency.NIDT) {
//      var symbol =
//          symbolService.findByCondition(TradeType.SPOT, CurrencyPair.valueOf(currency, Currency.JPY));
//      var candlestickService = CandlestickService.getBean(symbol);
//      var dateTo = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
//      latest = candlestickService.findOneByCondition(symbol.getId(), CandlestickType.PT1M, null,
//          dateTo, false);
//    } else if (currency == Currency.ETH) {
//      var dateTo = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
//      latest = posCandlestickService.findOneByCondition(7l, CandlestickType.P1D, null,
//              dateTo, false);
//    }
//
//    if (latest == null) {
//        return new BigDecimal(0);
//    } else {
//        return latest.getClose();
//    }

	Candlestick latest = null;
    var symbol =
            symbolService.findByCondition(TradeType.SPOT, CurrencyPair.valueOf(currency, Currency.JPY));
    
    // UNYO-758 NIDTの場合、CBから取得、以外の場合、whaleFinから取得
    if (Currency.NIDT.equals(currency)) {
      var candlestickService = CandlestickService.getBean(symbol);
      var dateTo = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
      latest = candlestickService.findOneByCondition(symbol.getId(), CandlestickType.PT1M, null,
          dateTo, false);
    } else {
      List<CurrencyPairConfig> currencyPairConfig = currencyPairConfigService.findAllByCondition(TradeType.POS, CurrencyPair.valueOf(currency, Currency.JPY), true);
      if (CollectionUtils.isEmpty(currencyPairConfig)) {
        if (currency == Currency.ETH) {
          // ETH SYMBOL ID:7
        var dateTo = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
        latest = posCandlestickService.findOneByCondition(7l, CandlestickType.P1D, null,
                dateTo, false, false);
        } else {
          if (symbol != null) {
            var candlestickService = CandlestickService.getBean(symbol);
            var dateTo = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
            latest = candlestickService.findOneByCondition(symbol.getId(), CandlestickType.PT1M, null,
                dateTo, false);
          }
        }
      } else {
        symbol = symbolService.findByCondition(TradeType.POS, CurrencyPair.valueOf(currency, Currency.JPY));
        var dateTo = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
        latest = posCandlestickService.findOneByCondition(symbol.getId(), CandlestickType.PT1M, null,
                dateTo, false, true);
      }

    }
	
	if (latest == null) {
		return new BigDecimal(0);
	} else {
		return latest.getClose();
	}
  }


  public List<Deposit> getDeposits(Currency currency, LocalDate date) {
    var targetFrom = date.atStartOfDay(ZoneId.of("Asia/Tokyo")).minusDays(6).toInstant().toEpochMilli();
    var targetTo =
        date.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).toInstant().toEpochMilli();
    var deposits =
        depositService.findAllByCondition(null, currency, null, null, targetFrom, targetTo, DepositStatus.DONE);
    return deposits;
  }


  // for calculate
  record AccumulateRecord(BigDecimal jpyConversion, User user) {
  }

  public List<ExcessiveDepositByPeriod> listExcessiveDeposits(ReadData readData) {
    // accumulate by userid, currency
    var map = new HashMap<Long, AccumulateRecord>();

    for (var depositsByCurrency : readData.depositsByCurrencies) {
      var deposits = depositsByCurrency.deposits();
      var jpyPrice = depositsByCurrency.jpyPrice();

      for (var d : deposits) {
        var user = d.getUser();
        var jpyConversion = d.getAmount().multiply(jpyPrice);
        if (map.containsKey(user.getId())) {
          var record = map.get(user.getId());
          jpyConversion = jpyConversion.add(record.jpyConversion);
        }
        map.put(user.getId(), new AccumulateRecord(jpyConversion, user));
      }
    }

    // filter by 200万JPY
    var depositsByUser = map.values().stream()
        .filter(r -> r.jpyConversion.compareTo(new BigDecimal("2000000")) >= 0)
        .collect(Collectors.toList());

    var list = new ArrayList<ExcessiveDepositByPeriod>();
    for (var r : depositsByUser) {
      var dto = createRecordIfExcessiveDeposit(r, readData.date());
      if (dto != null) {
        list.add(dto);
      }
    }

    return list;
  }

  public ExcessiveDepositByPeriod createRecordIfExcessiveDeposit(AccumulateRecord record, LocalDate date) {
    var jpyConversion = record.jpyConversion;

    Integer financial_assets, income;
    if (record.user.getUserInfoCorporateId() != null) {
      financial_assets = record.user.getUserInfoCorporate().getFinancialAssets();
      income = financial_assets; // 法人の場合はincomeはないのでfinancial_assetsを入れる
    } else {
      // 個人
      financial_assets = record.user.getUserInfo().getFinancialAssets();
      income = record.user.getUserInfo().getIncome();
    }

    if (jpyConversion.compareTo(getIncomeConverter(income)) > 0
        || jpyConversion.compareTo(getIncomeConverter(financial_assets)) > 0) {
      var dto = new ExcessiveDepositByPeriod();
      dto.setUserId(record.user.getId());
      dto.setEmail(record.user.getEmail());
      dto.setAmount(record.jpyConversion); // Currencyで合計（例：BTC, ETH）になっているので、日本円に変換した値を入れる
      dto.setFinancialAssets(financial_assets);
      dto.setIncome(income);
      dto.setTmsStatus(TmsStatus.OPEN);
      dto.setTargetAt(new Date(date.atStartOfDay(ZoneId.of("Asia/Tokyo")).toInstant().toEpochMilli()));
      return dto;
    }

    return null;
  }

  // income converter
  // { value: 1, label: "100 万円未満" }, => 1000000
  // { value: 2, label: "100 万円 ~ 300 万円未満" }, => 3000000
  // { value: 3, label: "300 万円 ~ 500 万円未満" }, => 5000000
  // { value: 4, label: "500 万円 ~ 1,000 万円未満" }, => 10000000
  // { value: 5, label: "1,000 万円 ~ 2,000 万円未満" }, => 20000000
  // { value: 6, label: "2,000 万円 ~ 5,000 万円未満" }, => 50000000
  // { value: 7, label: "5,000 万円 ~ 1 億円未満" }, => 100000000
  // { value: 8, label: "1 億円以上" }, => 100000000
  public BigDecimal getIncomeConverter(int income) {
    switch (income) {
      case 1:
        return new BigDecimal(1000000);
      case 2:
        return new BigDecimal(3000000);
      case 3:
        return new BigDecimal(5000000);
      case 4:
        return new BigDecimal(10000000);
      case 5:
        return new BigDecimal(20000000);
      case 6:
        return new BigDecimal(50000000);
      case 7:
        return new BigDecimal(100000000);
      case 8:
        return new BigDecimal(100000000);
      default:
        return BigDecimal.ZERO;
    }
  }

  public ReadData read(List<Currency> currencies, LocalDate date) {
    var depositsByCurrency = new ArrayList<DepositsByCurrency>();
    for (var currency : currencies) {
      var deposits = getDeposits(currency, date);
      var jpyPrice = getJpyPrice(currency, date);
      depositsByCurrency.add(new DepositsByCurrency(currency, jpyPrice, deposits));
    }
    return new ReadData(depositsByCurrency, date);
  }

  public WriteData process(ReadData readData) {
    var deposits = listExcessiveDeposits(readData);
    return new WriteData(deposits, readData.date);
  }

  public void write(WriteData writeData) {
    for (var d : writeData.ExcessiveDeposits()) {
      save(d);
    }
  }

  public void execute(List<Currency> currencies, LocalDate date) {
    deleteByTargetAt(date);
    var readData = read(currencies, date);
    var writeData = process(readData);
    write(writeData);
  }

  @Override
  public Class<ExcessiveDepositByPeriod> getEntityClass() {
    return ExcessiveDepositByPeriod.class;
  }
}
