package exchange.common.config;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.NameValuePair;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import lombok.Getter;
import lombok.Setter;

@Configuration
@ConfigurationProperties(prefix = "elliptic")
@Getter
@Setter
public class EllipticConfig {

  private String key;

  private String secret;
  
  private String baseEndpoint;
  
  private String analysesUri;
  
  private String resultUri;
  
  private Float riskScoreLimit;
  
  public String getPathofAnalysesUri() {
    return baseEndpoint + analysesUri;
  }
  
  public String getPathofResultUri() {
    return baseEndpoint + resultUri;
  }
  
  /** createHeader 
   * @throws NoSuchAlgorithmException 
   * @throws InvalidKeyException */
  public String createSign(String secret, String seed) throws NoSuchAlgorithmException, InvalidKeyException {
    Mac hmac = Mac.getInstance("HmacSHA256");
    SecretKeySpec secret_key = new SecretKeySpec(Base64.decodeBase64(secret), "HmacSHA256");
    hmac.init(secret_key);
    hmac.update(seed.getBytes());
    return Base64.encodeBase64String(hmac.doFinal());
  }
  
  public Map<String, String> createHeaderMap(
      String method, String path, String body) throws InvalidKeyException, NoSuchAlgorithmException {
    Map<String, String> headerMap = new HashMap<>();
    String nonce = String.valueOf(System.currentTimeMillis());
    headerMap.put("x-access-key", getKey());
    if (body == null) {
      headerMap.put("x-access-sign", createSign(getSecret(), nonce + method + path.toLowerCase() + "{}"));
    } else {
      headerMap.put("x-access-sign", createSign(getSecret(), nonce + method + path.toLowerCase() + body));
    }
    headerMap.put("x-access-timestamp", nonce);
    return headerMap;
  }
  
  public Map<String, String> createHeaderMapGet(
      HttpMethod method, String path, List<NameValuePair> nameValuePairs) throws InvalidKeyException, NoSuchAlgorithmException {
    if (!CollectionUtils.isEmpty(nameValuePairs)) {
      StringJoiner stringJoiner = new StringJoiner("&");
      nameValuePairs.forEach(
          nameValuePair ->
              stringJoiner.add(nameValuePair.getName() + "=" + nameValuePair.getValue()));
      path = path + "?" + stringJoiner.toString();
    }

    return createHeaderMap(method.toString(), path, null);
  }
}
