package exchange.pos.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.OrderStatus;
import exchange.common.entity.Trade_;
import exchange.common.service.EntityService;
import exchange.pos.entity.PosCustomizeOrder;
import exchange.pos.predicate.PosCustomizeOrderPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosCustomizeOrderService  extends EntityService<PosCustomizeOrder, PosCustomizeOrderPredicate> {

  @Override
  public Class<PosCustomizeOrder> getEntityClass() {
    return PosCustomizeOrder.class;
  }
  
  public List<PosCustomizeOrder> findUnFullyFilledOrderByCondition(
      Long symbolId,
      List<OrderStatus> orderStatus,
      BigDecimal remainingAmount) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCustomizeOrder, List<PosCustomizeOrder>>() {

          @Override
          public List<PosCustomizeOrder> query() {
            List<Predicate> predicates =
                createPredicatesOfFindByCondition(
                    criteriaBuilder,
                    root,
                    symbolId,
                    CollectionUtils.isEmpty(orderStatus) ? null : orderStatus.toArray(new OrderStatus[orderStatus.size()]),
                    remainingAmount);
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.asc(root.get(Trade_.id)));
          }
        });
  }

  private List<Predicate> createPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<PosCustomizeOrder> root,
      Long symbolId,
      OrderStatus[] orderStatus,
      BigDecimal remainingAmount) {
    List<Predicate> predicates = new ArrayList<>();
      if(symbolId != null) {
        predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
      }
      if(orderStatus != null) {
        predicates.add(predicate.inOrderStatus(root, orderStatus));
      }
      if(remainingAmount != null) {
        predicates.add(predicate.greaterThanRemainingAmount(criteriaBuilder, root, remainingAmount));
      }
      return predicates;
    }
  
}
