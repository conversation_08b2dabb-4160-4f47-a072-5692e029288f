-- DB構造変更
CREATE TABLE `dow_jones_antisocial_check` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `user_id` bigint DEFAULT NULL COMMENT 'ユーザーID',
 `user_type` int NOT NULL COMMENT 'type(1、個人 2、法人)',
 `api_type` int NOT NULL COMMENT 'type(1、Risk & Compliance Search API 2、Screening and Monitoring API)',
 `business_type` int NOT NULL COMMENT 'antisocial check business type(1、user info change 2、crypto token deposit 3、crypto token withdrawal 4、user continuous screening)',
 `business_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'business id',
 `user_info_id` bigint DEFAULT NULL COMMENT '個人ユーザー情報ID',
 `user_info_corporate_id` bigint DEFAULT NULL COMMENT '法人ユーザー情報ID',
 `user_relation_data_info_list` json DEFAULT NULL COMMENT 'user relation data info list',
 `case_id` bigint DEFAULT NULL COMMENT 'dow jones sam case id',
 `has_risk` bit(1) DEFAULT NULL COMMENT 'リスクがあるかどうか',
 `start_time` datetime(3) NOT NULL COMMENT 'start time',
 `end_time` datetime(3) NOT NULL COMMENT 'end time',
 `duration_millis` bigint NOT NULL COMMENT 'duration millis',
 `trace_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'trace id',
 `error_reason` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'error reason',
 `error_msg` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'error msg',
 `error_stack_trace` LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'error stack trace',
 `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
 `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
 PRIMARY KEY (`id`),
 KEY `idx_user_id` (`user_id`) USING BTREE,
 KEY `idx_user_type` (`user_type`) USING BTREE,
 KEY `idx_api_type` (`api_type`) USING BTREE,
 KEY `idx_business_type` (`business_type`) USING BTREE,
 KEY `idx_business_id` (`business_id`) USING BTREE,
 KEY `idx_user_info_id` (`user_info_id`) USING BTREE,
 KEY `idx_user_info_corporate_id` (`user_info_corporate_id`) USING BTREE,
 KEY `idx_case_id` (`case_id`) USING BTREE,
 KEY `idx_has_risk` (`has_risk`) USING BTREE,
 KEY `idx_trace_id` (`trace_id`) USING BTREE,
 KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='dow jones antisocial check';

CREATE TABLE `dow_jones_sam_case` (
`id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
`dow_jones_sam_case_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'dow jones sam case id',
`dow_jones_sam_case_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'dow jones sam case name',
`dow_jones_sam_external_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'dow jones sam external id',
`search_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'search type(Broad、Near、Precise)',
`user_id` bigint NOT NULL COMMENT 'ユーザーID',
`user_type` int NOT NULL COMMENT 'type(1、個人 2、法人)',
`user_info_sync_time` datetime(3) DEFAULT NULL COMMENT 'user info sync time',
`has_risk` bit(1) DEFAULT NULL COMMENT 'リスクがあるかどうか',
`enabled` bit(1) NOT NULL DEFAULT b'1' COMMENT '有効',
`created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
`updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
PRIMARY KEY (`id`),
KEY `idx_dow_jones_sam_case_id` (`dow_jones_sam_case_id`) USING BTREE,
KEY `idx_user_id` (`user_id`) USING BTREE,
KEY `idx_user_type` (`user_type`) USING BTREE,
KEY `idx_user_info_sync_time` (`user_info_sync_time`) USING BTREE,
KEY `idx_has_risk` (`has_risk`) USING BTREE,
KEY `idx_enabled` (`enabled`) USING BTREE,
KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='dow jones sam case';

CREATE TABLE `dow_jones_sam_association` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
 `case_id` bigint NOT NULL COMMENT 'case id',
 `dow_jones_sam_association_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'dow jones sam association id',
 `user_relation_data_type` varchar(32) NOT NULL COMMENT 'type： 1、USER(個人) 2、CORPORATE(法人) 3、REPRESENTATIVE(代表者) 4、AGENT(取引担当者) 6、OWNER(UBO)',
 `user_relation_data_id` bigint NOT NULL COMMENT 'user relation data id',
 `user_relation_data_info` json DEFAULT NULL COMMENT 'user relation data info',
 `enabled` bit(1) NOT NULL DEFAULT b'1' COMMENT '有効',
 `created_at` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '作成日時',
 `updated_at` timestamp(3) NULL DEFAULT NULL COMMENT '更新日時',
 PRIMARY KEY (`id`),
 KEY `idx_case_id` (`case_id`) USING BTREE,
 KEY `idx_dow_jones_sam_association_id` (`dow_jones_sam_association_id`) USING BTREE,
 KEY `idx_user_relation_data_type` (`user_relation_data_type`) USING BTREE,
 KEY `idx_user_relation_data_id` (`user_relation_data_id`) USING BTREE,
 KEY `idx_enabled` (`enabled`) USING BTREE,
 KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='dow jones sam association';

-- DBデータ変更
INSERT INTO `worker_master`( `environment`, `bean_name`, `trade_type`, `currency_pair`, `interval_millis`, `enabled`) VALUES
    ('stg', 'dowJonesSamCaseUpdater', null, NULL, NULL, 1);
INSERT INTO `worker_master`( `environment`, `bean_name`, `trade_type`, `currency_pair`, `interval_millis`, `enabled`) VALUES
    ('stg', 'userDailyAntiSocialChecker', null, NULL, NULL, 1);
