package exchange.worker.worker;

import java.util.Map;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import exchange.worker.component.Worker;
import exchange.common.entity.Symbol;
import exchange.common.service.TradesService;

@RequiredArgsConstructor
@Component
public class TradesMaker extends Worker {

  private final TradesService tradesService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
    tradesService.create(symbol);
  }
}
